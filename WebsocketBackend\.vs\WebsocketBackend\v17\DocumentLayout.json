{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|c:\\users\\<USER>\\onedrive\\desktop\\websocket\\websocketbackend\\services\\websocketmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|solutionrelative:services\\websocketmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|c:\\users\\<USER>\\onedrive\\desktop\\websocket\\websocketbackend\\middleware\\websocketmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|solutionrelative:middleware\\websocketmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|c:\\users\\<USER>\\onedrive\\desktop\\websocket\\websocketbackend\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|c:\\users\\<USER>\\onedrive\\desktop\\websocket\\websocketbackend\\model\\message.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AAA4B4A-4344-4115-A6FD-420B412CEB34}|WebsocketBackend.csproj|solutionrelative:model\\message.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T12:21:21.719Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "WebSocketMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\Middleware\\WebSocketMiddleware.cs", "RelativeDocumentMoniker": "Middleware\\WebSocketMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\Middleware\\WebSocketMiddleware.cs", "RelativeToolTip": "Middleware\\WebSocketMiddleware.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T12:19:53.813Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "WebSocketManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\Services\\WebSocketManager.cs", "RelativeDocumentMoniker": "Services\\WebSocketManager.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\Services\\WebSocketManager.cs*", "RelativeToolTip": "Services\\WebSocketManager.cs*", "ViewState": "AgIAACIAAAAAAAAAAAA2wMAAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T12:18:43.094Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Message.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\model\\Message.cs", "RelativeDocumentMoniker": "model\\Message.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\WebSocket\\WebsocketBackend\\model\\Message.cs", "RelativeToolTip": "model\\Message.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADcAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T12:17:23.894Z"}]}]}]}