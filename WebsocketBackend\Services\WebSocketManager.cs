﻿using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using WebsocketBackend.model;

namespace WebsocketBackend.Services {
    public class WebSocketManager {
        private readonly ConcurrentDictionary<string, WebSocket> _connections = new();
        private readonly ConcurrentDictionary<string, string> _userConnections = new();
        private readonly ConcurrentDictionary<string, HashSet<string>> _groupMembers = new();

        public async Task HandleWebSocketAsync(WebSocket webSocket, string connectionId) {
            _connections[connectionId] = webSocket;

            var buffer = new byte[1024 * 4];

            try {
                while (webSocket.State == WebSocketState.Open) {
                    var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);

                    if (result.MessageType == WebSocketMessageType.Text) {
                        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        await ProcessMessage(connectionId, message);
                    }
                    else if (result.MessageType == WebSocketMessageType.Close) {
                        await HandleDisconnection(connectionId);
                        break;
                    }
                }
            }
            catch (Exception ex) {
                await HandleDisconnection(connectionId);
            }
        }

        private async Task ProcessMessage(string connectionId, string message) {

            var wsMessage = JsonSerializer.Deserialize<WebSocketMessage>(message);
            if (wsMessage == null) return;

            switch (wsMessage.Type) {
                case "JoinChat":
                    await HandleJoinChat(connectionId, wsMessage.Data);
                    break;
                case "SendPrivateMessage":
                    await HandlePrivateMessage(connectionId, wsMessage.Data);
                    break;
                case "SendGroupMessage":
                    await HandleGroupMessage(connectionId, wsMessage.Data);
                    break;
                case "CreateGroup":
                    await HandleCreateGroup(connectionId, wsMessage.Data);
                    break;
                case "JoinGroup":
                    await HandleJoinGroup(connectionId, wsMessage.Data);
                    break;

            }

        }

        private async Task HandleJoinChat(string connectionId, object? data) {
            UserJoinData? joinData = null;

            if (data is JsonElement jsonElement) {
                joinData = JsonSerializer.Deserialize<UserJoinData>(jsonElement.GetRawText());
            }
            else {
                var jsonString = JsonSerializer.Serialize(data);
                joinData = JsonSerializer.Deserialize<UserJoinData>(jsonString);
            }


            if (joinData?.Username == null) return;

            _userConnections[connectionId] = joinData.Username;

            // Notify all users about new user
            await BroadcastToAll("UserJoined", joinData.Username);

            // Send current users list to new user
            var users = _userConnections.Values.Distinct().ToList();
            await SendToConnection(connectionId, "UsersList", users);

            // Send current groups list to new user
            var groups = _groupMembers.Keys.ToList();
            await SendToConnection(connectionId, "GroupsList", groups);
        }

        private async Task HandlePrivateMessage(string connectionId, object? data) {
            PrivateMessageData? msgData = null;

            if (data is JsonElement jsonElement) {
                msgData = JsonSerializer.Deserialize<PrivateMessageData>(jsonElement.GetRawText());
            }
            else {
                var jsonString = JsonSerializer.Serialize(data);
                msgData = JsonSerializer.Deserialize<PrivateMessageData>(jsonString);
            }


            if (msgData == null) return;

            var fromUser = _userConnections.GetValueOrDefault(connectionId);
            if (fromUser == null) return;

            var toConnectionId = _userConnections.FirstOrDefault(x => x.Value == msgData.ToUser).Key;

            var chatMessage = new ChatMessage {
                From = fromUser,
                To = msgData.ToUser,
                Content = msgData.Message,
                Timestamp = DateTime.Now
            };

            // Send to recipient
            if (toConnectionId != null) {
                await SendToConnection(toConnectionId, "ReceivePrivateMessage", chatMessage);
            }

            // Send back to sender
            await SendToConnection(connectionId, "ReceivePrivateMessage", chatMessage);
        }

        private async Task HandleGroupMessage(string connectionId, object? data) {
            GroupMessageData? msgData = null;

            if (data is JsonElement jsonElement) {
                msgData = JsonSerializer.Deserialize<GroupMessageData>(jsonElement.GetRawText());
            }
            else {
                var jsonString = JsonSerializer.Serialize(data);
                msgData = JsonSerializer.Deserialize<GroupMessageData>(jsonString);
            }


            if (msgData == null) return;

            var fromUser = _userConnections.GetValueOrDefault(connectionId);
            if (fromUser == null) return;

            var chatMessage = new ChatMessage {
                From = fromUser,
                Content = msgData.Message,
                GroupName = msgData.GroupName,
                IsGroup = true,
                Timestamp = DateTime.Now
            };

            // Send to all group members
            if (_groupMembers.ContainsKey(msgData.GroupName)) {
                var groupUsers = _groupMembers[msgData.GroupName];
                foreach (var user in groupUsers) {
                    var userConnectionId = _userConnections.FirstOrDefault(x => x.Value == user).Key;
                    if (userConnectionId != null) {
                        await SendToConnection(userConnectionId, "ReceiveGroupMessage", chatMessage);
                    }
                }
            }
        }

        private async Task HandleCreateGroup(string connectionId, object? data) {
            GroupData? groupData = null;

            if (data is JsonElement jsonElement) {
                groupData = JsonSerializer.Deserialize<GroupData>(jsonElement.GetRawText());
            }
            else {
                var jsonString = JsonSerializer.Serialize(data);
                groupData = JsonSerializer.Deserialize<GroupData>(jsonString);
            }


            if (groupData?.GroupName == null) return;

            var username = _userConnections.GetValueOrDefault(connectionId);
            if (username == null) return;

            _groupMembers[groupData.GroupName] = new HashSet<string> { username };

            await SendToConnection(connectionId, "GroupCreated", groupData.GroupName);

            var groupsList = _groupMembers.Keys.ToList();
            await BroadcastToAll("GroupsList", groupsList);
        }

        private async Task HandleJoinGroup(string connectionId, object? data) {
            GroupData? groupData = null;

            if (data is JsonElement jsonElement) {
                groupData = JsonSerializer.Deserialize<GroupData>(jsonElement.GetRawText());
            }
            else {
                var jsonString = JsonSerializer.Serialize(data);
                groupData = JsonSerializer.Deserialize<GroupData>(jsonString);
            }


            if (groupData?.GroupName == null) return;

            var username = _userConnections.GetValueOrDefault(connectionId);
            if (username == null) return;

            if (_groupMembers.ContainsKey(groupData.GroupName)) {
                _groupMembers[groupData.GroupName].Add(username);

                // Notify group members
                var groupUsers = _groupMembers[groupData.GroupName];
                foreach (var user in groupUsers) {
                    var userConnectionId = _userConnections.FirstOrDefault(x => x.Value == user).Key;
                    if (userConnectionId != null) {
                        await SendToConnection(userConnectionId, "UserJoinedGroup", new { username, groupName = groupData.GroupName });
                    }
                }

                await SendToConnection(connectionId, "JoinedGroup", groupData.GroupName);
            }
        }

        private async Task HandleDisconnection(string connectionId) {
            if (_userConnections.TryRemove(connectionId, out var username)) {
                // Remove from all groups
                foreach (var group in _groupMembers) {
                    if (group.Value.Contains(username)) {
                        group.Value.Remove(username);

                        // Notify remaining group members
                        foreach (var user in group.Value) {
                            var userConnectionId = _userConnections.FirstOrDefault(x => x.Value == user).Key;
                            if (userConnectionId != null) {
                                await SendToConnection(userConnectionId, "UserLeftGroup", new { username, groupName = group.Key });
                            }
                        }
                    }
                }

                // Notify all users
                await BroadcastToAll("UserLeft", username);
            }

            _connections.TryRemove(connectionId, out _);
        }

        private async Task SendToConnection(string connectionId, string type, object data) {
            if (_connections.TryGetValue(connectionId, out var webSocket) && webSocket.State == WebSocketState.Open) {
                var message = new WebSocketMessage { Type = type, Data = data };
                var json = JsonSerializer.Serialize(message);
                var buffer = Encoding.UTF8.GetBytes(json);

                await webSocket.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Text, true, CancellationToken.None);
            }
        }

        private async Task BroadcastToAll(string type, object data) {
            var tasks = new List<Task>();

            foreach (var connectionId in _connections.Keys) {
                tasks.Add(SendToConnection(connectionId, type, data));
            }

            await Task.WhenAll(tasks);
        }
    }
}
