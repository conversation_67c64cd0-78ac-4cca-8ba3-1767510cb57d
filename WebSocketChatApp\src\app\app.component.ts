import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { WebsocketService } from './Services/websocket.service';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [CommonModule, FormsModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  username = '';
  tempUsername = '';
  users: string[] = [];
  groups: string[] = [];
  selectedChat = '';
  isGroupChat = false;
  newMessage = '';
  newGroupName = '';
  isConnected = false;

  privateMessages: { [key: string]: any[] } = {};
  groupMessages: { [key: string]: any[] } = {};
  unreadCounts: { [key: string]: number } = {};

  constructor(private wsService: WebsocketService) {}

  ngOnInit() {
    // Connection status
    this.wsService.connectionStatus$.subscribe((status) => {
      this.isConnected = status;
    });

    // User events
    this.wsService.userJoined$.subscribe((user) => {
      console.log('User joined:', user);
      if (!this.users.includes(user) && user !== this.username) {
        this.users.push(user);
        console.log('Updated users list:', this.users);
      }
    });

    this.wsService.userLeft$.subscribe((user) => {
      console.log('User left:', user);
      this.users = this.users.filter((u) => u !== user);
      console.log('Updated users list after user left:', this.users);
    });

    this.wsService.usersList$.subscribe((users) => {
      console.log('Received users list from server:', users);
      this.users = users.filter((u) => u !== this.username);
      console.log('Filtered users list (excluding self):', this.users);
    });

    // Group events
    this.wsService.groupsList$.subscribe((groups) => {
      console.log('Received groups list from server:', groups);
      this.groups = groups;
      console.log('Updated groups list:', this.groups);
    });

    this.wsService.groupCreated$.subscribe((groupName) => {
      console.log('Group created:', groupName);
      // Don't manually add to groups array, wait for GroupsList update from server
    });

    // Message events
    this.wsService.privateMessage$.subscribe((msg) => {
      const chatKey = msg.from === this.username ? msg.to : msg.from;
      if (!this.privateMessages[chatKey]) {
        this.privateMessages[chatKey] = [];
      }
      this.privateMessages[chatKey].push(msg);

      // Update unread count
      if (
        msg.from !== this.username &&
        (this.selectedChat !== chatKey || this.isGroupChat)
      ) {
        this.unreadCounts[chatKey] = (this.unreadCounts[chatKey] || 0) + 1;
      }
    });

    this.wsService.groupMessage$.subscribe((msg) => {
      if (!this.groupMessages[msg.groupName]) {
        this.groupMessages[msg.groupName] = [];
      }
      this.groupMessages[msg.groupName].push(msg);

      // Update unread count
      if (
        msg.from !== this.username &&
        (this.selectedChat !== msg.groupName || !this.isGroupChat)
      ) {
        const key = `group_${msg.groupName}`;
        this.unreadCounts[key] = (this.unreadCounts[key] || 0) + 1;
      }
    });
  }

  ngOnDestroy() {
    this.wsService.disconnect();
  }

  async joinChat() {
    if (this.tempUsername.trim()) {
      this.username = this.tempUsername.trim();
      try {
        await this.wsService.connect();
        // Wait a bit to ensure connection is fully established
        setTimeout(() => {
          this.wsService.joinChat(this.username);
        }, 100);
      } catch (error) {
        console.error('Failed to connect:', error);
        this.username = '';
      }
    }
  }

  leaveChat() {
    this.wsService.disconnect();
    this.username = '';
    this.tempUsername = '';
    this.selectedChat = '';
    this.users = [];
    this.groups = [];
    this.privateMessages = {};
    this.groupMessages = {};
    this.unreadCounts = {};
    this.isConnected = false;
  }

  selectPrivateChat(user: string) {
    if (user !== this.username) {
      this.selectedChat = user;
      this.isGroupChat = false;
      // Clear unread count
      delete this.unreadCounts[user];
    }
  }

  selectGroupChat(group: string) {
    this.selectedChat = group;
    this.isGroupChat = true;
    // Clear unread count
    delete this.unreadCounts[`group_${group}`];
  }

  sendMessage() {
    if (this.newMessage.trim() && this.selectedChat && this.isConnected) {
      if (this.isGroupChat) {
        this.wsService.sendGroupMessage(this.selectedChat, this.newMessage);
      } else {
        this.wsService.sendPrivateMessage(this.selectedChat, this.newMessage);
      }
      this.newMessage = '';
    }
  }

  createGroup() {
    if (this.newGroupName.trim() && this.isConnected) {
      this.wsService.createGroup(this.newGroupName);
      this.newGroupName = '';
    }
  }

  joinGroup(groupName: string) {
    if (this.isConnected) {
      this.wsService.joinGroup(groupName);
    }
  }

  getCurrentMessages() {
    if (!this.selectedChat) return [];

    if (this.isGroupChat) {
      return this.groupMessages[this.selectedChat] || [];
    } else {
      return this.privateMessages[this.selectedChat] || [];
    }
  }

  getUnreadCount(chatKey: string, isGroup: boolean = false): number {
    const key = isGroup ? `group_${chatKey}` : chatKey;
    return this.unreadCounts[key] || 0;
  }
}
