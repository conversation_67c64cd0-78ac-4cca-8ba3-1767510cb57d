.chat-container {
  height: 100vh;
  font-family: Arial, sans-serif;
}

.login-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 1rem;
}

.chat-app {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 280px;
  background: #f5f5f5;
  border-right: 1px solid #ddd;
  padding: 1rem;
  overflow-y: auto;
}

.user-info {
  margin-bottom: 2rem;
  text-align: center;
}

.connection-status {
  font-size: 0.8em;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  margin-top: 0.5rem;
}

.connection-status.connected {
  background: #d4edda;
  color: #155724;
}

.connection-status.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.section {
  margin-bottom: 2rem;
}

.section h4 {
  margin-bottom: 0.5rem;
  color: #666;
}

.user-item,
.group-name {
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-item:hover,
.group-name:hover {
  background: #e0e0e0;
}

.user-item.active,
.group-name.active {
  background: #007bff;
  color: white;
}

.group-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.group-name {
  flex: 1;
  margin-right: 0.5rem;
}

.unread-badge {
  background: #dc3545;
  color: white;
  border-radius: 10px;
  padding: 0.125rem 0.375rem;
  font-size: 0.7em;
  font-weight: bold;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 1rem;
  border-bottom: 1px solid #ddd;
  background: white;
}

.messages {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background: #fafafa;
}

.message {
  margin-bottom: 1rem;
  padding: 0.5rem;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message.own-message {
  background: #007bff;
  color: white;
  margin-left: 2rem;
}

.message-header {
  display: flex;
  justify-content: space-between;
  font-size: 0.8em;
  margin-bottom: 0.25rem;
}

.sender {
  font-weight: bold;
}

.timestamp {
  opacity: 0.7;
}

.no-messages {
  text-align: center;
  color: #666;
  font-style: italic;
  margin-top: 2rem;
}

.message-input {
  display: flex;
  padding: 1rem;
  gap: 0.5rem;
  border-top: 1px solid #ddd;
  background: white;
}

.input-field {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 1;
}

.input-field:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

.input-field.small {
  flex: 1;
}

.btn-primary {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 0.25rem 0.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8em;
}

.btn-small {
  padding: 0.25rem 0.5rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8em;
}

.btn-tiny {
  padding: 0.125rem 0.25rem;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 0.7em;
}

button:hover:not(:disabled) {
  opacity: 0.9;
}
