// WebSocket message interfaces matching the C# backend models

export interface WebSocketMessage {
  type: string;
  data: any;
}

export interface ChatMessage {
  from: string;
  to: string;
  content: string;
  timestamp: Date;
  isGroup: boolean;
  groupName: string;
}

export interface UserJoinData {
  username: string;
}

export interface PrivateMessageData {
  toUser: string;
  message: string;
}

export interface GroupMessageData {
  groupName: string;
  message: string;
}

export interface GroupData {
  groupName: string;
}

export interface UserGroupEvent {
  username: string;
  groupName: string;
}

// Message types that can be sent to the server
export type OutgoingMessageType = 
  | 'JoinChat'
  | 'SendPrivateMessage'
  | 'SendGroupMessage'
  | 'CreateGroup'
  | 'JoinGroup';

// Message types that can be received from the server
export type IncomingMessageType = 
  | 'UserJoined'
  | 'UserLeft'
  | 'UsersList'
  | 'GroupsList'
  | 'ReceivePrivateMessage'
  | 'ReceiveGroupMessage'
  | 'GroupCreated'
  | 'JoinedGroup'
  | 'UserJoinedGroup'
  | 'UserLeftGroup';
