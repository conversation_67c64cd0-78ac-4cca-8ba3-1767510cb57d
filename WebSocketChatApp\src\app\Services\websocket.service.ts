import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import {
  WebSocketMessage,
  ChatMessage,
  UserJoinData,
  PrivateMessageData,
  GroupMessageData,
  GroupData,
  UserGroupEvent,
} from '../models/websocket.models';

@Injectable({
  providedIn: 'root',
})
export class WebsocketService {
  private socket: WebSocket | null = null;
  private readonly wsUrl = 'ws://localhost:5063/ws';

  // Observables for different message types
  private userJoinedSubject = new Subject<string>();
  private userLeftSubject = new Subject<string>();
  private usersListSubject = new BehaviorSubject<string[]>([]);
  private groupsListSubject = new BehaviorSubject<string[]>([]);
  private privateMessageSubject = new Subject<ChatMessage>();
  private groupMessageSubject = new Subject<ChatMessage>();
  private groupCreatedSubject = new Subject<string>();
  private joinedGroupSubject = new Subject<string>();
  private userJoinedGroupSubject = new Subject<UserGroupEvent>();
  private userLeftGroupSubject = new Subject<UserGroupEvent>();
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);

  // Public observables
  public userJoined$ = this.userJoinedSubject.asObservable();
  public userLeft$ = this.userLeftSubject.asObservable();
  public usersList$ = this.usersListSubject.asObservable();
  public groupsList$ = this.groupsListSubject.asObservable();
  public privateMessage$ = this.privateMessageSubject.asObservable();
  public groupMessage$ = this.groupMessageSubject.asObservable();
  public groupCreated$ = this.groupCreatedSubject.asObservable();
  public joinedGroup$ = this.joinedGroupSubject.asObservable();
  public userJoinedGroup$ = this.userJoinedGroupSubject.asObservable();
  public userLeftGroup$ = this.userLeftGroupSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();

  constructor() {}

  // Connect to WebSocket server
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = new WebSocket(this.wsUrl);

        this.socket.onopen = () => {
          console.log('WebSocket connected');
          this.connectionStatusSubject.next(true);
          resolve();
        };

        this.socket.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.socket.onclose = () => {
          console.log('WebSocket disconnected');
          this.connectionStatusSubject.next(false);
          this.socket = null;
        };

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.connectionStatusSubject.next(false);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.connectionStatusSubject.next(false);
    }
  }

  // Check if connected
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  // Send message to server
  private sendMessage(type: string, data: any): void {
    if (this.isConnected()) {
      const message: WebSocketMessage = { type, data };
      this.socket!.send(JSON.stringify(message));
    } else {
      console.error('WebSocket is not connected');
    }
  }

  // Handle incoming messages from server
  private handleMessage(data: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(data);
      console.log('Received WebSocket message:', message);

      switch (message.type) {
        case 'UserJoined':
          console.log('Processing UserJoined:', message.data);
          this.userJoinedSubject.next(message.data);
          break;
        case 'UserLeft':
          console.log('Processing UserLeft:', message.data);
          this.userLeftSubject.next(message.data);
          break;
        case 'UsersList':
          console.log('Processing UsersList:', message.data);
          this.usersListSubject.next(message.data);
          break;
        case 'GroupsList':
          console.log('Processing GroupsList:', message.data);
          this.groupsListSubject.next(message.data);
          break;
        case 'ReceivePrivateMessage':
          console.log('Processing ReceivePrivateMessage:', message.data);
          this.privateMessageSubject.next(message.data);
          break;
        case 'ReceiveGroupMessage':
          console.log('Processing ReceiveGroupMessage:', message.data);
          this.groupMessageSubject.next(message.data);
          break;
        case 'GroupCreated':
          console.log('Processing GroupCreated:', message.data);
          this.groupCreatedSubject.next(message.data);
          break;
        case 'JoinedGroup':
          console.log('Processing JoinedGroup:', message.data);
          this.joinedGroupSubject.next(message.data);
          break;
        case 'UserJoinedGroup':
          console.log('Processing UserJoinedGroup:', message.data);
          this.userJoinedGroupSubject.next(message.data);
          break;
        case 'UserLeftGroup':
          console.log('Processing UserLeftGroup:', message.data);
          this.userLeftGroupSubject.next(message.data);
          break;
        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  }

  // Public methods matching backend functionality

  // Join chat with username
  joinChat(username: string): void {
    const data: UserJoinData = { username };
    console.log('Sending JoinChat message:', data);
    this.sendMessage('JoinChat', data);
  }

  // Send private message
  sendPrivateMessage(toUser: string, message: string): void {
    const data: PrivateMessageData = { toUser, message };
    this.sendMessage('SendPrivateMessage', data);
  }

  // Send group message
  sendGroupMessage(groupName: string, message: string): void {
    const data: GroupMessageData = { groupName, message };
    this.sendMessage('SendGroupMessage', data);
  }

  // Create new group
  createGroup(groupName: string): void {
    const data: GroupData = { groupName };
    console.log('Sending CreateGroup message:', data);
    this.sendMessage('CreateGroup', data);
  }

  // Join existing group
  joinGroup(groupName: string): void {
    const data: GroupData = { groupName };
    this.sendMessage('JoinGroup', data);
  }

  // Get current users list
  getCurrentUsers(): string[] {
    return this.usersListSubject.value;
  }

  // Get current groups list
  getCurrentGroups(): string[] {
    return this.groupsListSubject.value;
  }
}
