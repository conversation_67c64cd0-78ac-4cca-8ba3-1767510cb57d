# WebSocket Service Documentation

This Angular WebSocket service is designed to work with the C# WebSocket backend middleware. It provides a complete chat application functionality with private messaging and group chat features.

## Features

- **Real-time WebSocket connection** to the C# backend
- **Private messaging** between users
- **Group chat** functionality
- **User presence** tracking
- **Connection status** monitoring
- **Type-safe interfaces** matching the C# backend models
- **Reactive programming** with RxJS observables

## Backend Configuration

The service connects to the WebSocket endpoint at `ws://localhost:5063/ws` which matches your C# backend configuration:

```csharp
// C# Backend - Program.cs
app.UseWebSockets();
app.UseMiddleware<WebSocketMiddleware>();

// C# Backend - WebSocketMiddleware.cs
if (context.Request.Path == "/ws" && context.WebSockets.IsWebSocketRequest)
```

## Service Architecture

### Models (`websocket.models.ts`)

The service uses TypeScript interfaces that exactly match your C# backend models:

- `WebSocketMessage` - Base message structure
- `ChatMessage` - Chat message with metadata
- `UserJoinData` - User joining data
- `PrivateMessageData` - Private message payload
- `GroupMessageData` - Group message payload
- `GroupData` - Group operation data
- `UserGroupEvent` - User group join/leave events

### Service Methods

#### Connection Management
```typescript
connect(): Promise<void>          // Connect to WebSocket server
disconnect(): void                // Disconnect from server
isConnected(): boolean           // Check connection status
```

#### Chat Operations
```typescript
joinChat(username: string): void                    // Join chat with username
sendPrivateMessage(toUser: string, message: string): void  // Send private message
sendGroupMessage(groupName: string, message: string): void // Send group message
createGroup(groupName: string): void                // Create new group
joinGroup(groupName: string): void                  // Join existing group
```

#### Data Access
```typescript
getCurrentUsers(): string[]      // Get current users list
getCurrentGroups(): string[]     // Get current groups list
```

### Observables

The service provides reactive streams for all events:

```typescript
connectionStatus$: Observable<boolean>           // Connection status changes
userJoined$: Observable<string>                 // User joined notifications
userLeft$: Observable<string>                   // User left notifications
usersList$: Observable<string[]>                // Current users list
groupsList$: Observable<string[]>               // Current groups list
privateMessage$: Observable<ChatMessage>        // Private messages received
groupMessage$: Observable<ChatMessage>          // Group messages received
groupCreated$: Observable<string>               // Group creation confirmations
joinedGroup$: Observable<string>                // Group join confirmations
userJoinedGroup$: Observable<UserGroupEvent>    // User joined group events
userLeftGroup$: Observable<UserGroupEvent>      // User left group events
```

## Usage Example

### Basic Setup

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { WebsocketService } from './Services/websocket.service';
import { ChatMessage } from './models/websocket.models';

@Component({
  selector: 'app-chat',
  template: `<!-- Your template here -->`
})
export class ChatComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  
  constructor(private websocketService: WebsocketService) {}

  async ngOnInit() {
    // Connect to WebSocket
    await this.websocketService.connect();
    
    // Subscribe to events
    this.subscriptions.push(
      this.websocketService.privateMessage$.subscribe(message => {
        console.log('Received private message:', message);
      })
    );
    
    // Join chat
    this.websocketService.joinChat('YourUsername');
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.websocketService.disconnect();
  }
}
```

### Sending Messages

```typescript
// Send private message
sendPrivateMessage(toUser: string, message: string) {
  this.websocketService.sendPrivateMessage(toUser, message);
}

// Send group message
sendGroupMessage(groupName: string, message: string) {
  this.websocketService.sendGroupMessage(groupName, message);
}

// Create and join group
createGroup(groupName: string) {
  this.websocketService.createGroup(groupName);
}
```

### Handling Events

```typescript
setupEventHandlers() {
  // Handle user events
  this.websocketService.userJoined$.subscribe(username => {
    console.log(`${username} joined the chat`);
  });

  // Handle messages
  this.websocketService.privateMessage$.subscribe(message => {
    this.displayMessage(message);
  });

  // Handle connection status
  this.websocketService.connectionStatus$.subscribe(isConnected => {
    this.updateConnectionUI(isConnected);
  });
}
```

## Message Flow

### Outgoing Messages (Client → Server)

1. **JoinChat** - Join the chat with a username
2. **SendPrivateMessage** - Send a private message to another user
3. **SendGroupMessage** - Send a message to a group
4. **CreateGroup** - Create a new group
5. **JoinGroup** - Join an existing group

### Incoming Messages (Server → Client)

1. **UserJoined** - A user joined the chat
2. **UserLeft** - A user left the chat
3. **UsersList** - Current list of online users
4. **GroupsList** - Current list of available groups
5. **ReceivePrivateMessage** - Received a private message
6. **ReceiveGroupMessage** - Received a group message
7. **GroupCreated** - Confirmation of group creation
8. **JoinedGroup** - Confirmation of joining a group
9. **UserJoinedGroup** - A user joined a group
10. **UserLeftGroup** - A user left a group

## Error Handling

The service includes comprehensive error handling:

- Connection errors are logged and connection status is updated
- Message parsing errors are caught and logged
- WebSocket state is properly managed
- Automatic cleanup on disconnection

## Complete Example Component

See `chat-example.component.ts` for a complete implementation that demonstrates:

- Connection management
- User interface for chat
- Private and group messaging
- User and group lists
- Real-time notifications
- Message history

## Backend Compatibility

This service is fully compatible with your C# WebSocket backend:

- **WebSocketMiddleware.cs** - Handles WebSocket connections
- **WebSocketManager.cs** - Manages connections and message routing
- **Message.cs** - Defines data models
- **Program.cs** - Configures CORS and WebSocket middleware

The service maintains the exact same message structure and protocol as your C# backend implementation.
