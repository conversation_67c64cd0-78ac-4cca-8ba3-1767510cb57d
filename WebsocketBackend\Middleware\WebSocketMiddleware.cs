﻿using WebsocketBackend.Services;

namespace WebsocketBackend.Middleware {
    public class WebSocketMiddleware {
        private readonly RequestDelegate _next;
        private readonly Services.WebSocketManager _webSocketManager;

        public WebSocketMiddleware(RequestDelegate next, Services.WebSocketManager webSocketManager) {
            _next = next;
            _webSocketManager = webSocketManager;
        }

        public async Task InvokeAsync(HttpContext context) {
            if (context.Request.Path == "/ws" && context.WebSockets.IsWebSocketRequest) {
                var webSocket = await context.WebSockets.AcceptWebSocketAsync();
                var connectionId = Guid.NewGuid().ToString();
                await _webSocketManager.HandleWebSocketAsync(webSocket, connectionId);
            }
            else {
                await _next(context);
            }
        }
    }
}
