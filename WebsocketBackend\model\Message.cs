﻿using System.Text.Json.Serialization;

namespace WebsocketBackend.model {
    public class WebSocketMessage {
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }

    public class ChatMessage {
        [JsonPropertyName("from")]
        public string From { get; set; } = string.Empty;

        [JsonPropertyName("to")]
        public string To { get; set; } = string.Empty;

        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        [JsonPropertyName("isGroup")]
        public bool IsGroup { get; set; }

        [JsonPropertyName("groupName")]
        public string GroupName { get; set; } = string.Empty;
    }

    public class UserJoinData {
        [JsonPropertyName("username")]
        public string Username { get; set; } = string.Empty;
    }

    public class PrivateMessageData {
        [JsonPropertyName("toUser")]
        public string ToUser { get; set; } = string.Empty;

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;
    }

    public class GroupMessageData {
        [JsonPropertyName("groupName")]
        public string GroupName { get; set; } = string.Empty;

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;
    }

    public class GroupData {
        [JsonPropertyName("groupName")]
        public string GroupName { get; set; } = string.Empty;
    }
}
