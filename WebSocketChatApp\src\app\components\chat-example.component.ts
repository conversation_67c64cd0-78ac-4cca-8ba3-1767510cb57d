import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { WebsocketService } from '../Services/websocket.service';
import { ChatMessage } from '../models/websocket.models';

@Component({
  selector: 'app-chat-example',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="chat-container">
      <div class="connection-status">
        <span [class]="isConnected ? 'connected' : 'disconnected'">
          {{ isConnected ? 'Connected' : 'Disconnected' }}
        </span>
        <button (click)="toggleConnection()">
          {{ isConnected ? 'Disconnect' : 'Connect' }}
        </button>
      </div>

      <div *ngIf="!username && isConnected" class="username-input">
        <input [(ngModel)]="usernameInput" placeholder="Enter your username" />
        <button (click)="joinChat()">Join <PERSON><PERSON></button>
      </div>

      <div *ngIf="username" class="chat-interface">
        <div class="users-groups-panel">
          <div class="users-list">
            <h3>Online Users ({{ users.length }})</h3>
            <ul>
              <li *ngFor="let user of users" 
                  [class.current-user]="user === username"
                  (click)="selectPrivateChat(user)">
                {{ user }} {{ user === username ? '(You)' : '' }}
              </li>
            </ul>
          </div>

          <div class="groups-list">
            <h3>Groups ({{ groups.length }})</h3>
            <div class="group-creation">
              <input [(ngModel)]="newGroupName" placeholder="Group name" />
              <button (click)="createGroup()">Create</button>
            </div>
            <ul>
              <li *ngFor="let group of groups" (click)="selectGroupChat(group)">
                {{ group }}
                <button (click)="joinGroup(group)">Join</button>
              </li>
            </ul>
          </div>
        </div>

        <div class="chat-area">
          <div class="chat-header">
            <h3>{{ currentChatTitle }}</h3>
          </div>
          
          <div class="messages-container">
            <div *ngFor="let message of currentMessages" class="message"
                 [class.own-message]="message.from === username">
              <div class="message-header">
                <span class="sender">{{ message.from }}</span>
                <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>

          <div class="message-input" *ngIf="currentChatType">
            <input [(ngModel)]="messageInput" 
                   placeholder="Type a message..." 
                   (keyup.enter)="sendMessage()" />
            <button (click)="sendMessage()">Send</button>
          </div>
        </div>
      </div>

      <div class="notifications">
        <div *ngFor="let notification of notifications" class="notification">
          {{ notification }}
        </div>
      </div>
    </div>
  `,
  styles: [`
    .chat-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      font-family: Arial, sans-serif;
    }

    .connection-status {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
    }

    .connected { color: green; }
    .disconnected { color: red; }

    .username-input {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .chat-interface {
      display: flex;
      gap: 20px;
      height: 600px;
    }

    .users-groups-panel {
      width: 300px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .users-list, .groups-list {
      border: 1px solid #ccc;
      padding: 15px;
      border-radius: 5px;
    }

    .users-list ul, .groups-list ul {
      list-style: none;
      padding: 0;
      margin: 10px 0 0 0;
    }

    .users-list li, .groups-list li {
      padding: 8px;
      cursor: pointer;
      border-radius: 3px;
      margin-bottom: 5px;
    }

    .users-list li:hover, .groups-list li:hover {
      background-color: #f0f0f0;
    }

    .current-user {
      font-weight: bold;
      background-color: #e3f2fd;
    }

    .group-creation {
      display: flex;
      gap: 5px;
      margin-bottom: 10px;
    }

    .chat-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #ccc;
      border-radius: 5px;
    }

    .chat-header {
      padding: 15px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #ccc;
    }

    .messages-container {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
      background-color: #fafafa;
    }

    .message {
      margin-bottom: 15px;
      padding: 10px;
      border-radius: 5px;
      background-color: white;
    }

    .own-message {
      background-color: #e3f2fd;
      margin-left: 50px;
    }

    .message-header {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #666;
      margin-bottom: 5px;
    }

    .sender {
      font-weight: bold;
    }

    .message-input {
      display: flex;
      padding: 15px;
      border-top: 1px solid #ccc;
      gap: 10px;
    }

    .message-input input {
      flex: 1;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 3px;
    }

    .notifications {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 300px;
    }

    .notification {
      background-color: #4caf50;
      color: white;
      padding: 10px;
      margin-bottom: 5px;
      border-radius: 3px;
      animation: slideIn 0.3s ease-in;
    }

    @keyframes slideIn {
      from { transform: translateX(100%); }
      to { transform: translateX(0); }
    }

    input, button {
      padding: 8px 12px;
      border: 1px solid #ccc;
      border-radius: 3px;
    }

    button {
      background-color: #2196f3;
      color: white;
      cursor: pointer;
    }

    button:hover {
      background-color: #1976d2;
    }
  `]
})
export class ChatExampleComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  
  // Connection state
  isConnected = false;
  username = '';
  usernameInput = '';

  // Users and groups
  users: string[] = [];
  groups: string[] = [];

  // Chat state
  currentChatType: 'private' | 'group' | null = null;
  currentChatTarget = '';
  currentMessages: ChatMessage[] = [];
  allMessages: ChatMessage[] = [];

  // Input fields
  messageInput = '';
  newGroupName = '';

  // Notifications
  notifications: string[] = [];

  constructor(private websocketService: WebsocketService) {}

  ngOnInit() {
    this.setupSubscriptions();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.websocketService.disconnect();
  }

  private setupSubscriptions() {
    // Connection status
    this.subscriptions.push(
      this.websocketService.connectionStatus$.subscribe(status => {
        this.isConnected = status;
      })
    );

    // Users list
    this.subscriptions.push(
      this.websocketService.usersList$.subscribe(users => {
        this.users = users;
      })
    );

    // Groups list
    this.subscriptions.push(
      this.websocketService.groupsList$.subscribe(groups => {
        this.groups = groups;
      })
    );

    // User events
    this.subscriptions.push(
      this.websocketService.userJoined$.subscribe(user => {
        this.addNotification(`${user} joined the chat`);
      })
    );

    this.subscriptions.push(
      this.websocketService.userLeft$.subscribe(user => {
        this.addNotification(`${user} left the chat`);
      })
    );

    // Messages
    this.subscriptions.push(
      this.websocketService.privateMessage$.subscribe(message => {
        this.allMessages.push(message);
        this.updateCurrentMessages();
      })
    );

    this.subscriptions.push(
      this.websocketService.groupMessage$.subscribe(message => {
        this.allMessages.push(message);
        this.updateCurrentMessages();
      })
    );

    // Group events
    this.subscriptions.push(
      this.websocketService.groupCreated$.subscribe(groupName => {
        this.addNotification(`Group "${groupName}" created successfully`);
      })
    );

    this.subscriptions.push(
      this.websocketService.joinedGroup$.subscribe(groupName => {
        this.addNotification(`Joined group "${groupName}"`);
      })
    );
  }

  async toggleConnection() {
    if (this.isConnected) {
      this.websocketService.disconnect();
      this.username = '';
      this.currentChatType = null;
      this.currentMessages = [];
      this.allMessages = [];
    } else {
      try {
        await this.websocketService.connect();
      } catch (error) {
        console.error('Failed to connect:', error);
      }
    }
  }

  joinChat() {
    if (this.usernameInput.trim()) {
      this.username = this.usernameInput.trim();
      this.websocketService.joinChat(this.username);
      this.usernameInput = '';
    }
  }

  selectPrivateChat(user: string) {
    if (user !== this.username) {
      this.currentChatType = 'private';
      this.currentChatTarget = user;
      this.updateCurrentMessages();
    }
  }

  selectGroupChat(group: string) {
    this.currentChatType = 'group';
    this.currentChatTarget = group;
    this.updateCurrentMessages();
  }

  sendMessage() {
    if (!this.messageInput.trim() || !this.currentChatType) return;

    if (this.currentChatType === 'private') {
      this.websocketService.sendPrivateMessage(this.currentChatTarget, this.messageInput);
    } else if (this.currentChatType === 'group') {
      this.websocketService.sendGroupMessage(this.currentChatTarget, this.messageInput);
    }

    this.messageInput = '';
  }

  createGroup() {
    if (this.newGroupName.trim()) {
      this.websocketService.createGroup(this.newGroupName.trim());
      this.newGroupName = '';
    }
  }

  joinGroup(groupName: string) {
    this.websocketService.joinGroup(groupName);
  }

  private updateCurrentMessages() {
    if (!this.currentChatType) {
      this.currentMessages = [];
      return;
    }

    if (this.currentChatType === 'private') {
      this.currentMessages = this.allMessages.filter(msg => 
        !msg.isGroup && (
          (msg.from === this.username && msg.to === this.currentChatTarget) ||
          (msg.from === this.currentChatTarget && msg.to === this.username)
        )
      );
    } else if (this.currentChatType === 'group') {
      this.currentMessages = this.allMessages.filter(msg => 
        msg.isGroup && msg.groupName === this.currentChatTarget
      );
    }
  }

  private addNotification(message: string) {
    this.notifications.push(message);
    setTimeout(() => {
      this.notifications.shift();
    }, 3000);
  }

  get currentChatTitle(): string {
    if (!this.currentChatType) return 'Select a chat';
    if (this.currentChatType === 'private') return `Private chat with ${this.currentChatTarget}`;
    return `Group: ${this.currentChatTarget}`;
  }

  formatTime(timestamp: Date): string {
    return new Date(timestamp).toLocaleTimeString();
  }
}
