<div class="chat-container">
  <div *ngIf="!username" class="login-form">
    <h2>Join <PERSON></h2>
    <input
      [(ngModel)]="tempUsername"
      placeholder="Enter username"
      (keyup.enter)="joinChat()"
      class="input-field"
    />
    <button (click)="joinChat()" class="btn-primary">Join</button>
  </div>

  <div *ngIf="username" class="chat-app">
    <div class="sidebar">
      <div class="user-info">
        <h3>{{ username }}</h3>
        <button (click)="leaveChat()" class="btn-secondary">Leave</button>
        <div
          class="connection-status"
          [class.connected]="isConnected"
          [class.disconnected]="!isConnected"
        >
          {{ isConnected ? "Connected" : "Disconnected" }}
        </div>
      </div>

      <div class="section">
        <h4>Users ({{ users.length }})</h4>
        <div
          *ngFor="let user of users"
          (click)="selectPrivateChat(user)"
          [class.active]="selectedChat === user && !isGroupChat"
          class="user-item"
        >
          <span class="user-name">{{ user }}</span>
          <span *ngIf="getUnreadCount(user) > 0" class="unread-badge">{{
            getUnreadCount(user)
          }}</span>
        </div>
      </div>

      <div class="section">
        <h4>Groups ({{ groups.length }})</h4>
        <div class="group-controls">
          <input
            [(ngModel)]="newGroupName"
            placeholder="Group name"
            (keyup.enter)="createGroup()"
            class="input-field small"
          />
          <button (click)="createGroup()" class="btn-small">Create</button>
        </div>
        <div *ngFor="let group of groups" class="group-item">
          <div
            (click)="selectGroupChat(group)"
            [class.active]="selectedChat === group && isGroupChat"
            class="group-name"
          >
            #{{ group }}
            <span
              *ngIf="getUnreadCount(group, true) > 0"
              class="unread-badge"
              >{{ getUnreadCount(group, true) }}</span
            >
          </div>
          <button (click)="joinGroup(group)" class="btn-tiny">Join</button>
        </div>
      </div>
    </div>

    <div class="chat-area">
      <div class="chat-header">
        <h3 *ngIf="!isGroupChat && selectedChat">
          Chat with {{ selectedChat }}
        </h3>
        <h3 *ngIf="isGroupChat && selectedChat">#{{ selectedChat }}</h3>
        <h3 *ngIf="!selectedChat">Select a user or group to start chatting</h3>
      </div>

      <div class="messages" #messagesContainer>
        <div
          *ngFor="let msg of getCurrentMessages()"
          [class.own-message]="msg.from === username"
          class="message"
        >
          <div class="message-header">
            <span class="sender">{{ msg.from }}</span>
            <span class="timestamp">{{ msg.timestamp | date: "short" }}</span>
          </div>
          <div class="message-content">{{ msg.content }}</div>
        </div>
        <div
          *ngIf="getCurrentMessages().length === 0 && selectedChat"
          class="no-messages"
        >
          No messages yet. Start the conversation!
        </div>
      </div>

      <div class="message-input" *ngIf="selectedChat">
        <input
          [(ngModel)]="newMessage"
          placeholder="Type a message..."
          (keyup.enter)="sendMessage()"
          [disabled]="!isConnected"
          class="input-field"
        />
        <button
          (click)="sendMessage()"
          [disabled]="!isConnected || !newMessage.trim()"
          class="btn-primary"
        >
          Send
        </button>
      </div>
    </div>
  </div>
</div>
